@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 238 75% 59%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 238 75% 59%;
    --radius: 0.75rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar: 0 0% 100%;
    --sidebar-foreground: 222.2 84% 4.9%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 238 75% 59%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 238 75% 59%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* Smooth transitions */
* {
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
}

/* 3D Card Flip Effect */
.perspective-1000 {
  perspective: 1000px;
}

.transform-style-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

/* React-PDF styles for better text selection */
.react-pdf__Page {
  margin: 0 auto;
  position: relative;
}

.react-pdf__Page__canvas {
  display: block;
  margin: 0 auto;
}

.react-pdf__Page__textContent {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  opacity: 0.2;
  line-height: 1.0;
  pointer-events: auto;
}

.react-pdf__Page__textContent span {
  color: transparent;
  position: absolute;
  white-space: pre;
  cursor: text;
  transform-origin: 0% 0%;
}

.react-pdf__Page__annotations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

/* Text selection styles */
.react-pdf__Page__textContent ::selection {
  background: rgba(255, 255, 0, 0.3);
}

.react-pdf__Page__textContent ::-moz-selection {
  background: rgba(255, 255, 0, 0.3);
}

/* PDF viewer specific styles */
.react-pdf__Document {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* PDF Reading Mode Styles */
.pdf-page-container {
  transition: all 0.3s ease;
}

/* Dark mode PDF styles */
.pdf-page-container.dark-mode .react-pdf__Page__canvas {
  filter: invert(1) hue-rotate(180deg) !important;
  background-color: #1f2937 !important;
}

.pdf-page-container.dark-mode .react-pdf__Page__textContent {
  filter: invert(1) hue-rotate(180deg) !important;
}

.pdf-page-container.dark-mode .react-pdf__Page__textContent span {
  color: rgba(255, 255, 255, 0.1) !important;
}

/* Sepia mode PDF styles */
.pdf-page-container.sepia-mode .react-pdf__Page__canvas {
  filter: sepia(1) saturate(0.8) brightness(1.1) contrast(1.1) !important;
  background-color: #fef3c7 !important;
}

.pdf-page-container.sepia-mode .react-pdf__Page__textContent {
  filter: sepia(1) saturate(0.8) brightness(1.1) !important;
}

.pdf-page-container.sepia-mode .react-pdf__Page__textContent span {
  color: rgba(92, 51, 23, 0.1) !important;
}

/* Light mode PDF styles */
.pdf-page-container.light-mode .react-pdf__Page__canvas {
  filter: none !important;
  background-color: #ffffff !important;
}

.pdf-page-container.light-mode .react-pdf__Page__textContent {
  filter: none !important;
}

.pdf-page-container.light-mode .react-pdf__Page__textContent span {
  color: transparent !important;
}
